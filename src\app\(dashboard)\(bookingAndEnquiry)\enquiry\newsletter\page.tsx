"use client"

import { useState, useMemo } from "react"
import {
  Table,
  TableHeader,
  TableHead,
  TableBody,
  TableRow,
  TableCell,
} from "@/components/ui/table"
import { Input } from "@/components/ui/input"
import {
  Select,
  SelectTrigger,
  SelectValue,
  SelectContent,
  SelectItem,
} from "@/components/ui/select"
import { But<PERSON> } from "@/components/ui/button"
import { Eye, Trash } from "lucide-react"
import { PaginationControls } from "@/components/common/pagination/pagination-control"
import { Newsletter } from "@/types/booking/newsletter"

const newsletterData: Newsletter[] = [
  { id: 1, sn: 1, email: "<EMAIL>" },
  { id: 2, sn: 2, email: "<EMAIL>" },
  { id: 3, sn: 3, email: "<EMAIL>" },
  { id: 4, sn: 4, email: "<EMAIL>" },
  { id: 5, sn: 5, email: "<EMAIL>" },
  { id: 6, sn: 6, email: "<EMAIL>" },
  { id: 7, sn: 7, email: "<EMAIL>" },
  { id: 8, sn: 8, email: "<EMAIL>" },
  { id: 9, sn: 9, email: "<EMAIL>" },
  { id: 10, sn: 10, email: "<EMAIL>" },
]

export default function NewsletterPage() {
  const [searchTerm, setSearchTerm] = useState("")
  const [entriesPerPage, setEntriesPerPage] = useState(25)
  const [currentPage, setCurrentPage] = useState(1)

  // filter
  const filtered = useMemo(() => {
    if (!searchTerm) return newsletterData
    return newsletterData.filter((n) =>
      [String(n.sn), n.email].some((val) =>
        val.toLowerCase().includes(searchTerm.toLowerCase())
      )
    )
  }, [searchTerm])

  // pagination
  const totalPages = Math.ceil(filtered.length / entriesPerPage)
  const paginated = useMemo(() => {
    const start = (currentPage - 1) * entriesPerPage
    return filtered.slice(start, start + entriesPerPage)
  }, [filtered, currentPage, entriesPerPage])

  return (
    <div className="p-6 bg-white rounded-lg shadow-sm">
      <h2 className="text-2xl font-semibold mb-4">
        Newsletter Subscribers
      </h2>

      <div className="flex flex-wrap items-center justify-between mb-4 gap-4">
        <div className="flex items-center gap-2">
          <span>Show</span>
          <Select
            value={String(entriesPerPage)}
            onValueChange={(v) => {
              setEntriesPerPage(Number(v))
              setCurrentPage(1)
            }}
          >
            <SelectTrigger className="w-[80px]">
              <SelectValue placeholder="Entries" />
            </SelectTrigger>
            <SelectContent>
              {[10, 25, 50, 100].map((n) => (
                <SelectItem key={n} value={String(n)}>
                  {n}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          <span>entries</span>
        </div>

        <div className="flex items-center gap-2">
          <span>Search:</span>
          <Input
            placeholder="Search..."
            value={searchTerm}
            onChange={(e) => {
              setSearchTerm(e.target.value)
              setCurrentPage(1)
            }}
            className="w-auto"
          />
        </div>
      </div>

      <div className="overflow-x-auto">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>S.N.</TableHead>
              <TableHead>Email</TableHead>
              <TableHead>Options</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {paginated.length > 0 ? (
              paginated.map((row) => (
                <TableRow key={row.id}>
                  <TableCell>{row.sn}</TableCell>
                  <TableCell>{row.email}</TableCell>
                  <TableCell>
                    <div className="flex gap-2">
                      <Button
                        variant="outline"
                        size="icon"
                        onClick={() => console.log("View", row.id)}
                      >
                        <Eye className="h-4 w-4" />
                        <span className="sr-only">View</span>
                      </Button>
                      <Button
                        variant="destructive"
                        size="icon"
                        onClick={() => console.log("Delete", row.id)}
                      >
                        <Trash className="h-4 w-4" />
                        <span className="sr-only">Delete</span>
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell colSpan={3} className="h-24 text-center">
                  No subscribers found.
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>

      <div className="mt-4 flex justify-end">
        <PaginationControls
          currentPage={currentPage}
          totalPages={totalPages}
          totalEntries={filtered.length}
          entriesPerPage={entriesPerPage}
          onPageChange={setCurrentPage}
        />
      </div>
    </div>
  )
}
