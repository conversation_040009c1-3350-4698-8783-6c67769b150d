"use client";

import React, { useState } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { Button } from '@/components/ui/button';

interface HeroEntry {
  videoUrl: string;
  title: string;
  subtitle: string;
  images: string[];
}

// Demo singleton hero entry; replace with API data
const initialHero: HeroEntry | null = {
  videoUrl: 'https://www.youtube.com/embed/dQw4w9WgXcQ',
  title: 'Welcome to Our Site',
  subtitle: 'Discover amazing content that engages your audience effectively.',
  images: [
    '/images/random.jpeg',
    '/images/random.jpeg',
    '/images/random.jpeg',
  ],
};

const HeroListPage: React.FC = () => {
  const [hero, setHero] = useState<HeroEntry | null>(initialHero);
  const visibleImages = 2;

  const handleDelete = () => {
    // TODO: call API to delete hero
    console.log('Deleting hero section');
    setHero(null);
  };

  return (
    <div className="p-6 bg-gray-100 min-h-screen">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <h1 className="text-2xl font-semibold">Hero Section</h1>
        {hero ? (
          <Link href="/home/<USER>/edit/1">
            <button className="px-4 py-2 bg-brand text-white rounded hover:bg-brand/80">
              Edit Hero
            </button>
          </Link>
        ) : (
          <Link href="/home/<USER>/create">
            <button className="px-4 py-2 bg-brand text-white rounded hover:bg-brand/80">
              + Add Hero
            </button>
          </Link>
        )}
      </div>

      {/* Table */}
      <div className="bg-white rounded-lg shadow overflow-x-auto">
        <table className="min-w-full">
          <thead className="bg-gray-50">
            <tr>
              <th className="px-6 py-3 text-left text-sm font-medium text-gray-500 uppercase">
                Video
              </th>
              <th className="px-6 py-3 text-left text-sm font-medium text-gray-500 uppercase">
                Images
              </th>
              <th className="px-6 py-3 text-left text-sm font-medium text-gray-500 uppercase">
                Title
              </th>
              <th className="px-6 py-3 text-left text-sm font-medium text-gray-500 uppercase">
                Description
              </th>
              <th className="px-6 py-3"></th>
            </tr>
          </thead>
          <tbody>
            {hero && (
              <tr className="border-t">
                {/* Video */}
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-700">
                  <Link href={hero.videoUrl} target="_blank">
                    <span className="text-brand hover:underline">View</span>
                  </Link>
                </td>

                {/* Images */}
                <td className="px-6 py-4">
                  <div className="flex items-center space-x-2">
                    {hero.images.slice(0, visibleImages).map((src, idx) => (
                      <Image
                        key={idx}
                        src={src}
                        alt={`hero-img-${idx}`}
                        width={64}
                        height={64}
                        className="object-cover rounded"
                      />
                    ))}
                    {hero.images.length > visibleImages && (
                      <div className="h-16 w-16 flex items-center justify-center bg-gray-100 rounded text-gray-600 text-sm">
                        +{hero.images.length - visibleImages}
                      </div>
                    )}
                  </div>
                </td>

                {/* Title */}
                <td className="px-6 py-4 whitespace-normal text-sm text-gray-700">
                  {hero.title}
                </td>

                {/* Description */}
                <td className="px-6 py-4 whitespace-normal text-sm text-gray-700 truncate">
                  {hero.subtitle}
                </td>

                {/* Actions */}
                <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium space-x-2">
                  <Link href="/home/<USER>/edit/1">
                    <Button className="px-3 py-1 bg-brand text-white rounded hover:bg-brand/80">
                      Edit
                    </Button>
                  </Link>
                  <Button
                    onClick={handleDelete}
                    className="px-3 py-1 bg-red-600 text-white rounded hover:bg-red-700"
                  >
                    Delete
                  </Button>
                </td>
              </tr>
            )}
          </tbody>
        </table>
      </div>
    </div>
  );
};

export default HeroListPage;
