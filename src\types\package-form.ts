// types/package.ts

// Activity Category Types
export type ActivityCategory = 'trekking' | 'trail-running' | 'peak-climbing' | 'fastpacking';

export const ACTIVITY_CATEGORIES: Record<ActivityCategory, { label: string; slug: string; description: string }> = {
  'trekking': {
    label: 'Trekking',
    slug: 'trekking',
    description: 'Multi-day hiking adventures in the mountains'
  },
  'trail-running': {
    label: 'Trail Running',
    slug: 'trail-running', 
    description: 'Running adventures on mountain trails'
  },
  'peak-climbing': {
    label: 'Peak Climbing',
    slug: 'peak-climbing',
    description: 'Technical climbing expeditions to mountain peaks'
  },
  'fastpacking': {
    label: 'Fast Packing',
    slug: 'fastpacking',
    description: 'Lightweight, fast-paced trekking adventures'
  }
};

// Core Package Types
export interface PackageEntry {
  id: number;
  name: string;
  destination: string;
  region: string;
  activity: ActivityCategory;
  slug: string;
  published: boolean;
}

// Itinerary Types
export interface ItineraryItem {
  id: number;
  day: string;
  title: string;
  details: string;
  image?: string;
  imageFile?: File;
  heading: string;
  trekDistance: string;
  flightHours: string;
  drivingHour: string;
  highestAltitude: string;
  trekDuration: string;
}

// SEO Types
export interface SeoFields {
  metaTitle: string;
  metaDescription: string;
  metaKeywords: string;
  canonicalUrl: string;
}

// Content Section Types
export interface PhotoEntry {
  file: File | null;
  caption: string;
}

export interface InfoEntry {
  title: string;
  body: string;
  note: string;
}

// Package Form Data Types
export interface PackageFormData {
  // Basic Details
  packageName: string;
  region: string;
  accommodation: string;
  trailType: string;
  maxAltitude: string;
  groupSize: string;
  bestSeason: string;
  price: string;
  activityPerDay: string;
  grade: string;
  activity: ActivityCategory;
  slug: string;
  distance: string;
  daysNights: string;
  meals: string;
  discountPrice: string;
  transportation: string;
  imageAlt: string;
  bookingLink: string;
  overview: string;
  
  // File uploads
  mainImage?: File;
  thumbnail?: File;
  pdfFile?: File;
}

// Package Status Controls
export interface PackageStatusControls {
  published: boolean;
  tripOfTheMonth: boolean;
  popularTours: boolean;
  shortTrek: boolean;
}

// Content Sections Data
export interface PackageContentData {
  // Highlights Section
  highlightsTitle: string;
  highlightsBody: string;
  
  // Description Section
  descriptionTitle: string;
  descriptionBody: string;
  
  // Short Itinerary Section
  shortItineraryTitle: string;
  shortItineraryItems: string[];
  
  // Photo Section
  photoTitle: string;
  photos: PhotoEntry[];
  
  // Video Section
  videoTitle: string;
  youtubeLinks: string[];
  
  // Includes Section
  includesTitle: string;
  includesBody: string;
  
  // Excludes Section
  excludesTitle: string;
  excludesBody: string;
  
  // Map Section
  mapTitle: string;
  mapFile?: File;
  
  // Trip Info Section
  tripInfoTitle: string;
  tripInfos: InfoEntry[];
}

// Complete Package Data Structure
export interface PackageData {
  id?: number;
  formData: PackageFormData;
  statusControls: PackageStatusControls;
  contentData: PackageContentData;
  itineraryItems: ItineraryItem[];
  seo: SeoFields;
  schema: string;
}

// Component Props Types
export interface CollapsibleSectionProps {
  title: string;
  isOpen: boolean;
  onToggle: () => void;
  children: React.ReactNode;
}

export interface AddItineraryFormProps {
  editingItem: ItineraryItem | null;
  onAddItinerary: (data: Omit<ItineraryItem, 'id'>) => void;
  onUpdateItinerary: (data: ItineraryItem) => void;
  onCancelEdit: () => void;
}

export interface ItineraryListProps {
  items: ItineraryItem[];
  onEdit: (id: number) => void;
  onDelete: (id: number) => void;
}

export interface PackageSidebarProps {
  activeHighlight: HighlightSectionType;
  onHighlightChange: (highlight: HighlightSectionType) => void;
}

export interface TripHighlightsContentProps {
  activeHighlight: string;
  packageData?: PackageContentData;
  onContentChange?: (field: keyof PackageContentData, value: any) => void;
}

// Package Page Props
export interface PackagePageProps {
  mode: 'create' | 'edit';
  category?: ActivityCategory;
  packageId?: number;
  initialData?: PackageData;
  onSave?: (data: PackageData) => void;
  onCancel?: () => void;
}

// Package List Props  
export interface CategoryPackageListProps {
  category: ActivityCategory;
  packages?: PackageEntry[];
  onCreateNew?: () => void;
  onEdit?: (packageId: number) => void;
  onDelete?: (packageId: number) => void;
}

// API Response Types
export interface PackageApiResponse {
  success: boolean;
  data?: PackageData;
  message?: string;
}

export interface PackageListApiResponse {
  success: boolean;
  data?: PackageEntry[];
  message?: string;
}

// Form Validation Types
export interface PackageFormErrors {
  packageName?: string;
  region?: string;
  activity?: string;
  slug?: string;
  price?: string;
  // Add other validation fields as needed
}

// File Upload Types
export interface FileUploadProps {
  label: string;
  accept: string;
  showPreview?: boolean;
  previewSrc?: string;
  previewAlt?: string;
  onFileChange: (file: File | null) => void;
}

// Tab Types
export type PackageTabType = 'details' | 'itinerary' | 'equipment' | 'cost' | 'discount' | 'faq';

// Highlight Section Types
export type HighlightSectionType = 
  | 'highlights' 
  | 'description' 
  | 'shortItinerary' 
  | 'photo' 
  | 'video' 
  | 'includes' 
  | 'excludes' 
  | 'map' 
  | 'tripInfo';