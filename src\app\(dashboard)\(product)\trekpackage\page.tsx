"use client";

import React, { useState } from "react";
import <PERSON> from "next/link";
import { Button } from "@/components/ui/button";

interface PackageEntry {
  id: number;
  name: string;
  destination: string;
  region: string;
  activity: string;
  slug: string;
  published: boolean;
}

const initialPackages: PackageEntry[] = [
  {
    id: 1,
    name: "Khopra Ridge Trek",
    destination: "Nepal",
    region: "Annapurna Region",
    activity: "Trekking",
    slug: "khopra-ridge-trek",
    published: true,
  },
  {
    id: 2,
    name: "Everest Base Camp Trek",
    destination: "Nepal",
    region: "Everest Region",
    activity: "Trekking",
    slug: "everest-base-camp-trek",
    published: true,
  },
];

export default function PackageListPage() {
  const [packs, setPacks] = useState<PackageEntry[]>(initialPackages);

  const handleDelete = (id: number) => {
    if (confirm("Are you sure you want to delete this package?")) {
      setPacks((prev) => prev.filter((p) => p.id !== id));
    }
  };

  return (
    <div className="p-6 bg-gray-100 min-h-screen space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-semibold">All Packages</h1>
        <Link href="/packages/create">
          <Button>+ Add Package</Button>
        </Link>
      </div>

      <div className="bg-white rounded-lg shadow overflow-x-auto">
        <table className="min-w-full text-left">
          <thead className="bg-gray-50">
            <tr>
              <th className="px-6 py-3 text-sm font-medium text-gray-500 uppercase">SN</th>
              <th className="px-6 py-3 text-sm font-medium text-gray-500 uppercase">Package Name</th>
              <th className="px-6 py-3 text-sm font-medium text-gray-500 uppercase">Destination</th>
              <th className="px-6 py-3 text-sm font-medium text-gray-500 uppercase">Region</th>
              <th className="px-6 py-3 text-sm font-medium text-gray-500 uppercase">Activity</th>
              <th className="px-6 py-3 text-sm font-medium text-gray-500 uppercase">Slug</th>
              <th className="px-6 py-3 text-sm font-medium text-gray-500 uppercase">Published</th>
              <th className="px-6 py-3"></th>
            </tr>
          </thead>
          <tbody className="divide-y divide-gray-200">
            {packs.map((pkg, i) => (
              <tr key={pkg.id}>
                <td className="px-6 py-4">{i + 1}</td>
                <td className="px-6 py-4">{pkg.name}</td>
                <td className="px-6 py-4">{pkg.destination}</td>
                <td className="px-6 py-4">{pkg.region}</td>
                <td className="px-6 py-4">{pkg.activity}</td>
                <td className="px-6 py-4">{pkg.slug}</td>
                <td className="px-6 py-4">{pkg.published ? "Published" : "Draft"}</td>
                <td className="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                  <Link href={`/packages/edit/${pkg.id}`}>
                    <Button variant="outline" size="sm">Edit</Button>
                  </Link>
                  <Button variant="destructive" size="sm" onClick={() => handleDelete(pkg.id)}>
                    Delete
                  </Button>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
}
