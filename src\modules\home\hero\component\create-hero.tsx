"use client";

import React, { useState, ChangeEvent } from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { useRouter } from 'next/navigation';

const CreateHeroPage: React.FC = () => {
  const [videoUrl, setVideoUrl] = useState<string>('');
  const [title, setTitle] = useState<string>('');
  const [subtitle, setSubtitle] = useState<string>('');
  const [images, setImages] = useState<string[]>([]);
  const router = useRouter();

  const handleVideoUrlChange = (e: ChangeEvent<HTMLInputElement>) => {
    setVideoUrl(e.target.value);
  };

  const handleTitleChange = (e: ChangeEvent<HTMLInputElement>) => {
    setTitle(e.target.value);
  };

  const handleSubtitleChange = (e: ChangeEvent<HTMLInputElement>) => {
    setSubtitle(e.target.value);
  };

  const handleImageUpload = (e: ChangeEvent<HTMLInputElement>) => {
    if (!e.target.files) return;
    const urls = Array.from(e.target.files).map((file) => URL.createObjectURL(file));
    setImages([...images, ...urls]);
  };

  const handleRemoveImage = (index: number) => {
    setImages(images.filter((_, i) => i !== index));
  };

  const handleCreate = () => {
    console.log('Creating hero:', { videoUrl, title, subtitle, images });
    // TODO: call your API to save new hero
    router.push('/home/<USER>');
  };

  return (
    <div className="p-6 bg-gray-50 min-h-screen">
      <h2 className="text-2xl font-semibold mb-4">Add New Hero</h2>

      {/* Video URL Input */}
      <div className="mb-6">
        <label className="block mb-1 font-medium">Video URL</label>
        <input
          type="text"
          className="w-full border rounded px-3 py-2"
          value={videoUrl}
          onChange={handleVideoUrlChange}
          placeholder="Paste embed or direct video URL here"
        />
        {videoUrl && (
          <div className="mt-4">
            <iframe
              className="w-full h-64 rounded"
              src={videoUrl}
              title="Preview Video"
              frameBorder="0"
              allowFullScreen
            />
          </div>
        )}
      </div>

      {/* Title Input */}
      <div className="mb-6">
        <label className="block mb-1 font-medium">Title</label>
        <input
          type="text"
          className="w-full border rounded px-3 py-2"
          value={title}
          onChange={handleTitleChange}
          placeholder="Enter hero title"
        />
      </div>

      {/* Subtitle Input */}
      <div className="mb-6">
        <label className="block mb-1 font-medium">Subtitle</label>
        <input
          type="text"
          className="w-full border rounded px-3 py-2"
          value={subtitle}
          onChange={handleSubtitleChange}
          placeholder="Enter hero subtitle"
        />
      </div>

      {/* Images Upload */}
      <div className="mb-6">
        <label className="block mb-1 font-medium">Images</label>
        <input
          type="file"
          accept="image/*"
          multiple
          onChange={handleImageUpload}
        />
        <div className="mt-4 grid grid-cols-3 gap-4">
          {images.map((src, idx) => (
            <div key={idx} className="relative">
              <Image
                src={src}
                alt={`Uploaded ${idx}`}
                width={150}
                height={100}
                className="w-full h-32 object-cover rounded"
              />
              <button
                onClick={() => handleRemoveImage(idx)}
                className="absolute top-1 right-1 text-white bg-black bg-opacity-50 rounded-full p-1"
              >
                ✕
              </button>
            </div>
          ))}
        </div>
      </div>

      {/* Actions */}
      <div className="mt-6 flex space-x-2">
        <button
          onClick={handleCreate}
          className="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700"
        >
          Create
        </button>
        <Link href="/home/<USER>">
          <button className="px-4 py-2 bg-gray-400 text-white rounded hover:bg-gray-500">
            Cancel
          </button>
        </Link>
      </div>
    </div>
  );
};

export default CreateHeroPage;
